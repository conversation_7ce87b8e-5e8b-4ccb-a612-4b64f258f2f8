import QtQuick
import QtQuick.Controls.Material 
import QtQuick.Layouts
import models 1.0
Item {
    id: root
    width: parent.width
    height: parent.height
    visible: true
    property var model
    property string buttonType: "Camera"
    property bool isPlayingStream: false
    property bool isOpenTabActive: false
    property var itemName: ""
    property bool isHoverControl: false
    property bool isHoverVideo: false
    property bool isComboBoxOpen: false
    property bool isHovered: isHoverControl || isHoverVideo || isComboBoxOpen

    // Timer to handle smooth transitions when ComboBox closes
    Timer {
        id: comboBoxCloseTimer
        interval: 100  // Small delay to prevent flickering
        onTriggered: {
            if (!isHoverControl && !isHoverVideo) {
                isComboBoxOpen = false
            }
        }
    }

    // Emit signal when hover state changes
    onIsHoverControlChanged: {
        console.log("isHoverControl changed:", isHoverControl)
        hoverStateChanged(isHovered)
    }
    onIsComboBoxOpenChanged: {
        console.log("isComboBoxOpen changed:", isComboBoxOpen)
        hoverStateChanged(isHovered)
    }
    onIsHoverVideoChanged: {
        console.log("isHoverVideo changed:", isHoverVideo)
        hoverStateChanged(isHovered)
    }
    signal closeSignal()
    signal fullScreenSignal()
    signal changeTabWidgetOpenSignal(bool open)
    signal hoverStateChanged(bool isHovering)
    z: 1000
    Component.onDestruction: {
        isPlayingStream = false
    }
    MapState {
        id:idMapState
    }
    // Modern approach using HoverHandler - more reliable for complex UIs
    HoverHandler {
        id: hoverHandler
        onHoveredChanged: {
            isHoverControl = hovered;
        }
    }

    // Fallback MouseArea for older Qt versions or additional mouse handling
    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true
        propagateComposedEvents: true
        acceptedButtons: Qt.NoButton  // Don't accept any mouse buttons, only handle hover
        onEntered: {
            if (!hoverHandler.hovered) {
                isHoverControl = true;
            }
        }
        onExited: {
            if (!hoverHandler.hovered) {
                isHoverControl = false;
            }
        }
    }
    Loader {
        id: buttonLoader
        anchors.fill: parent
        sourceComponent: (function(){
            return buttonType === "Camera" ? idCamera : idBuilding
        })()
    }
    Component {
        id: idCamera
        Rectangle{
            width: parent.width
            height: parent.width
            color: backgroundColor
            topLeftRadius: 10
            topRightRadius: 10

            Timer {
                id: updateTimer
                interval: 1000
                running: true
                repeat: true
                onTriggered: {
                    let date = new Date();
                    timeText.text = (date.getMonth() + 1) + "/" + date.getDate() + "/" + date.getFullYear() +
                                    " " + date.getHours().toString().padStart(2, '0') + ":" +
                                    date.getMinutes().toString().padStart(2, '0') + ":" +
                                    date.getSeconds().toString().padStart(2, '0');
                }
            }
            ColumnLayout{
                anchors.fill: parent
                spacing: 0
                RowLayout{
                    spacing: 0
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 50
                    Item{
                        width: 5
                        height: 50
                    }
                    Image{
                        source: cameraItemIcon
                        width: 20
                        height: 20
                        fillMode: Image.PreserveAspectFit
                        sourceSize: Qt.size(width, height)
                        Layout.alignment: Qt.AlignLeft | Qt.AlignVCenter
                    }
                    Text{
                        text: itemName
                        color: textColor
                        elide: Text.ElideRight
                        wrapMode: Text.NoWrap
                        clip: true
                        font.bold: true
                        font.pixelSize: 12
                        Layout.preferredWidth: 120
                        Layout.leftMargin: 5
                        Layout.alignment: Qt.AlignLeft | Qt.AlignVCenter
                    }
                    Text {
                        id: timeText
                        text: "Loading..."
                        color: textColor
                        font.pixelSize: 12
                        Layout.leftMargin: 15
                        Layout.alignment: Qt.AlignLeft | Qt.AlignVCenter
                    }
                    Item { Layout.fillWidth: true }
                    Button{
                        property bool isExpand: false
                        icon.source: isExpand ? iconCollapseCamera : iconExpandCamera
                        icon.color: "transparent"
                        icon.width: 20
                        icon.height: 20
                        Layout.leftMargin: 25
                        Layout.alignment: Qt.AlignRight | Qt.AlignVCenter
                        background: Rectangle{
                            color: "transparent"
                            border.color: "transparent"
                        }
                        onClicked: {
                            isExpand = !isExpand
                            fullScreenSignal()
                        }
                    }
                    Button{
                        icon.source: iconCloseCamera
                        icon.color: "transparent"
                        icon.width: 20
                        icon.height: 20
                        Layout.alignment: Qt.AlignRight | Qt.AlignVCenter
                        background: Rectangle{
                            color: "transparent"
                            border.color: "transparent"
                        }
                        onClicked: {
                            isPlayingStream = false
                            closeSignal()
                        }
                    }
                }

                CustomVideoOutput {
                    width: 515
                    height: 300
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.margins: 2
                    id: videoFrame
                    isPlaying: isPlayingStream
                    model: root.model
                    MouseArea {
                        id: videoMouseArea
                        anchors.fill: parent
                        hoverEnabled: true
                        propagateComposedEvents: true  // Đảm bảo sự kiện không bị chặn

                        onEntered: {
                            root.isHoverVideo = true;
                        }
                        onExited: {
                            root.isHoverVideo = false;
                        }
                    }
                }
            }
        }
        
    }
    Component {
        id: idBuilding

        Rectangle{
            width: parent.width
            height: parent.width

            color: backgroundColor
            topLeftRadius: 10
            topRightRadius: 10

            property var currentFloorModel: root.model && root.model.floorIds.length > 0 ? root.model.floorIds[0]: undefined
            ColumnLayout{
                anchors.fill: parent
                spacing: 0
                RowLayout{
                    spacing: 0
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 50
                    Item { 
                        width: 10
                    }
                    ComboBox {
                        id: comboBox
                        width: 200
                        visible: currentFloorModel === undefined ? false : true
                        model: root.model.floorIds
                        currentIndex: 0
                        textRole: "name"
                        onCurrentIndexChanged: {
                            currentFloorModel = root.model.floorIds[currentIndex]
                            // console.log("currentFloorModel ",currentFloorModel)
                        }

                        // Track popup open/close state to maintain hover
                        onPopupVisibleChanged: {
                            root.isComboBoxOpen = popup.visible
                            console.log("ComboBox popup visible:", popup.visible, "isComboBoxOpen:", root.isComboBoxOpen)
                        }
                        background: Rectangle {
                            color: backgroundColor
                            implicitWidth: 120
                            implicitHeight: 30
                            border.color: borderColor
                            border.width: 1
                            radius: 4
                        }
                        contentItem: Item {
                            id: clipper
                            anchors {
                                left: parent.left;    leftMargin: 8
                                right: parent.right;  rightMargin: quality_icon_indicator.width + 8
                                verticalCenter: parent.verticalCenter
                            }
                            clip: true

                            Text {
                                text: comboBox.currentText
                                color: textColor
                                anchors.fill: parent
                                font.pixelSize: 12
                                wrapMode: Text.NoWrap
                                elide: Text.ElideRight
                                horizontalAlignment: Text.AlignLeft
                                verticalAlignment: Text.AlignVCenter
                            }
                        }
                        delegate: ItemDelegate {
                            width: comboBox.width
                            hoverEnabled: true

                            onClicked: {
                                comboBox.currentIndex = index
                                comboBox.popup.close()
                            }

                            MouseArea {
                                id: tooltipHoverArea
                                anchors.fill: parent
                                hoverEnabled: true
                                propagateComposedEvents: true
                                onClicked: mouse.accepted = false // Pass the click through
                                onPressed: mouse.accepted = false // Pass the press through
                                onReleased: mouse.accepted = false // Pass the release through

                                ToolTip {
                                    visible: tooltipHoverArea.containsMouse
                                    delay: 300
                                    contentItem: Text {
                                        text: modelData.name
                                        color: "white"
                                        wrapMode: Text.Wrap
                                        width: tooltipHoverArea.width
                                        font.pixelSize: 12
                                    }
                                    background: Rectangle {
                                        color: "#0F1123"
                                        radius: 5
                                    }
                                }
                            }
                            contentItem: Text {
                                text: modelData.name
                                color: textColor
                                font.pixelSize: 12
                                verticalAlignment: Text.AlignVCenter
                                horizontalAlignment: Text.AlignHCenter
                                elide: Text.ElideRight
                            }
                            background: Rectangle {
                                color: backgroundColor
                                border.color: borderColor
                                border.width: 2
                            }
                        }

                        indicator: Image {
                            id: quality_icon_indicator
                            source: comboboxIcon
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.right: parent.right
                            anchors.rightMargin: 8
                        }

                        popup: Popup {
                            y: comboBox.height - 1
                            width: comboBox.width
                            height: Math.min(contentItem.implicitHeight, comboBox.Window.height - topMargin - bottomMargin)
                            padding: 1

                            // Add hover handling to the popup
                            onOpened: {
                                root.isComboBoxOpen = true
                                comboBoxCloseTimer.stop()  // Cancel any pending close
                            }
                            onClosed: {
                                // Use timer for smooth transition
                                comboBoxCloseTimer.start()
                            }

                            contentItem: ListView {
                                clip: true
                                implicitHeight: contentHeight
                                model: comboBox.popup.visible ? comboBox.delegateModel : null
                                currentIndex: comboBox.highlightedIndex

                                // Add hover detection to the ListView
                                MouseArea {
                                    anchors.fill: parent
                                    hoverEnabled: true
                                    propagateComposedEvents: true
                                    acceptedButtons: Qt.NoButton
                                    onEntered: {
                                        root.isComboBoxOpen = true
                                    }
                                }
                            }

                            background: Rectangle {
                                border.color: borderColor
                                radius: 2
                            }
                        }
                    }
                    Item { Layout.fillWidth: true }
                    Button{
                        property bool isExpand: false
                        icon.source: isExpand ? iconCollapseCamera : iconExpandCamera
                        icon.color: "transparent"
                        icon.width: 25
                        icon.height: 25
                        Layout.leftMargin: 35
                        Layout.alignment: Qt.AlignRight | Qt.AlignVCenter
                        background: Rectangle{
                            color: "transparent"
                            border.color: "transparent"
                        }
                        onClicked: {
                            console.log("abcd = ",parent.width,parent.height,parent)
                            isExpand = !isExpand
                            fullScreenSignal()
                        }
                    }
                    Button{
                        icon.source: iconCloseCamera
                        icon.color: "transparent"
                        icon.width: 20
                        icon.height: 20
                        // Layout.fillWidth: true
                        // Layout.preferredWidth: 30
                        Layout.alignment: Qt.AlignRight | Qt.AlignVCenter
                        background: Rectangle{
                            color: "transparent"
                            border.color: "transparent"
                        }
                        onClicked: {
                            isPlayingStream = false
                            closeSignal()
                        }
                    }
                }
                Loader {
                    id: contentLoader
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    sourceComponent: (function(){
                        if (currentFloorModel === undefined)
                        {
                            console.log("contentLoader = ",currentFloorModel);
                        }
                        return currentFloorModel === undefined ? idEmpty : idMap2DonGrid
                    })()
                }
                Component {
                    id: idMap2DonGrid
                    Map2DonGrid {
                        qmlType: true
                        floorModelFromQML: (function(){
                            return currentFloorModel
                        })()
                        mapStateFromQML: idMapState
                    }
                }
                Component {
                    id: idEmpty
                    Rectangle {
                        color: backgroundColor
                        Text {
                            text: qsTr("No floor plan available")
                            font.pixelSize: 14
                            color: textColor
                            anchors.centerIn: parent 
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                    }
                }
            }
        }
        
    }
    Component.onCompleted: {
        // console.log("PreviewItem.qml loaded ",model.floorIds);
    }

    property color backgroundColor: idMapState ? idMapState.get_color_theme_by_key("main_background") : "white"
    property color borderColor: idMapState ? idMapState.get_color_theme_by_key("main_border") : "white"
    property color textColor: idMapState ? idMapState.get_color_theme_by_key("text_color_all_app") : "white"
    property color primaryColor: idMapState ? idMapState.get_color_theme_by_key("primary") : "white"
    property string iconExpandCamera: idMapState ? idMapState.get_image_theme_by_key("expand_camera_on_map") : "white"
    property string iconCloseCamera: idMapState ? idMapState.get_image_theme_by_key("close_camera_on_map") : "white"
    property string iconCollapseCamera: idMapState ? idMapState.get_image_theme_by_key("collapse_camera_on_map") : "white"
    property string cameraItemIcon: idMapState ? idMapState.get_image_theme_by_key("camera_item") : "white"
    property string comboboxIcon: idMapState ? idMapState.get_image_theme_by_key("down_spinbox_temp") : "white"

    Connections{
        target: idMapState
        function onThemeChanged(){
            backgroundColor = idMapState.get_color_theme_by_key("main_background")
            borderColor = idMapState.get_color_theme_by_key("main_border")
            textColor = idMapState.get_color_theme_by_key("text_color_all_app")
            primaryColor = idMapState.get_color_theme_by_key("primary")
            cameraItemIcon = idMapState.get_image_theme_by_key("camera_item")
            iconExpandCamera = idMapState.get_image_theme_by_key("expand_camera_on_map")
            iconCollapseCamera = idMapState.get_image_theme_by_key("collapse_camera_on_map")
            iconCloseCamera = idMapState.get_image_theme_by_key("close_camera_on_map")
            comboboxIcon = idMapState.get_image_theme_by_key("down_spinbox_temp")
        }
    }
}

